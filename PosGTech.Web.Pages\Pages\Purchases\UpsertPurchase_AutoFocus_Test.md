# اختبار التركيز التلقائي على حقل المستودع في صفحة UpsertPurchase

## التغييرات المنفذة

### 1. إضافة دالة OnAfterRenderAsync
تم إضافة دالة `OnAfterRenderAsync` في ملف `UpsertPurchase.razor.cs` لتنفيذ التركيز التلقائي على حقل المستودع عند تحميل الصفحة لأول مرة.

```csharp
protected override async Task OnAfterRenderAsync(bool firstRender)
{
    if (firstRender)
    {
        // التركيز على حقل المستودع عند تحميل الصفحة لأول مرة
        await FocusStoreFieldOnLoad();
    }
    await base.OnAfterRenderAsync(firstRender);
}
```

### 2. إضافة دالة FocusStoreFieldOnLoad
تم إضافة دالة مساعدة للتعامل مع التركيز على حقل المستودع مع معالجة الأخطاء:

```csharp
/// <summary>
/// التركيز على حقل المستودع عند تحميل الصفحة
/// </summary>
private async Task FocusStoreFieldOnLoad()
{
    try
    {
        // انتظار قصير للتأكد من أن العنصر قد تم عرضه بالكامل
        await Task.Delay(100);
        
        if (StoreField != null)
        {
            await StoreField.FocusAsync();
        }
    }
    catch (Exception ex)
    {
        // تجاهل الأخطاء المتعلقة بالتركيز لتجنب تعطيل الصفحة
        // يمكن إضافة تسجيل الأخطاء هنا إذا لزم الأمر
    }
}
```

## التحقق من التنفيذ

### المراجع المطلوبة
- ✅ `@ref="StoreField"` موجود في ملف `.razor` (السطر 81)
- ✅ `MudSelect<Guid?> StoreField { get; set; }` معرّف في ملف `.cs` (السطر 89)

### دوال دورة الحياة
- ✅ `OnInitializedAsync` موجودة ومحدثة
- ✅ `OnAfterRenderAsync` مضافة حديثاً
- ✅ `FocusStoreFieldOnLoad` مضافة حديثاً

## خطوات الاختبار

1. **فتح صفحة إنشاء فاتورة شراء جديدة:**
   - انتقل إلى `/upsertPurchase/00000000-0000-0000-0000-000000000000`
   - تحقق من أن المؤشر يظهر تلقائياً في حقل "المخازن"

2. **فتح صفحة تعديل فاتورة موجودة:**
   - انتقل إلى فاتورة موجودة
   - تحقق من أن المؤشر يظهر تلقائياً في حقل "المخازن"

3. **التحقق من عدم تعارض الوظائف:**
   - تأكد من أن جميع الوظائف الأخرى تعمل بشكل طبيعي
   - تأكد من أن التنقل بين الحقول يعمل بشكل صحيح
   - تأكد من أن حفظ البيانات يعمل بشكل طبيعي

## الفوائد المحققة

1. **تحسين تجربة المستخدم:** المؤشر يظهر تلقائياً في الحقل الأول المطلوب
2. **توفير الوقت:** لا حاجة للنقر على حقل المستودع يدوياً
3. **تدفق عمل أفضل:** يمكن للمستخدم البدء فوراً في إدخال البيانات
4. **معالجة الأخطاء:** الكود محمي ضد أي أخطاء محتملة في التركيز

## ملاحظات تقنية

- تم استخدام `Task.Delay(100)` لضمان أن العنصر قد تم عرضه بالكامل قبل محاولة التركيز
- تم تطبيق معالجة الأخطاء لتجنب تعطيل الصفحة في حالة فشل التركيز
- التركيز يحدث فقط في أول عرض للصفحة (`firstRender = true`)
- الكود متوافق مع نمط Blazor ودورة حياة المكونات
